package com.maguo.loan.cash.flow.enums;

import java.util.Arrays;

/**
 * 还款方式枚举类
 * 用于映射还款方式代码到具体名称
 */
public enum RepayMethodEnum {

    /**
     * 等额本息
     */
    EQUAL_PRINCIPAL_INTEREST("1", "DEBIT");

    private final String code;
    private final String name;

    RepayMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据代码获取还款方式枚举
     *
     * @param code 还款方式代码
     * @return 对应的还款方式枚举，如果找不到返回null
     */
    public static RepayMethodEnum fromCode(String code) {
        return Arrays.stream(values())
            .filter(method -> method.getCode().equals(code))
            .findFirst()
            .orElse(null);
    }

    /**
     * 根据代码获取还款方式名称
     *
     * @param code 还款方式代码
     * @return 对应的还款方式名称，如果找不到返回null
     */
    public static String getNameByCode(String code) {
        RepayMethodEnum method = fromCode(code);
        return method != null ? method.getName() : null;
    }
}
